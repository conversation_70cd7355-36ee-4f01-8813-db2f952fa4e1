"""
通用工具函数
"""
import random
import numpy as np
import torch


def set_random_seed(seed):
    """
    设置所有随机数种子以确保结果可复现
    
    Args:
        seed (int): 随机数种子
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    
    # 确保CUDA操作的确定性
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    print(f"Random seed set to: {seed}")


def get_device():
    """
    获取可用的设备
    
    Returns:
        str: 设备名称 ('cuda' 或 'cpu')
    """
    return "cuda" if torch.cuda.is_available() else "cpu"


def count_parameters(model):
    """
    计算模型的参数数量
    
    Args:
        model: PyTorch模型
        
    Returns:
        int: 可训练参数的数量
    """
    return sum(p.numel() for p in model.parameters() if p.requires_grad)


def save_checkpoint(model, optimizer, epoch, loss, filepath):
    """
    保存模型检查点
    
    Args:
        model: PyTorch模型
        optimizer: 优化器
        epoch: 当前epoch
        loss: 当前损失
        filepath: 保存路径
    """
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': loss,
    }
    torch.save(checkpoint, filepath)
    print(f"Checkpoint saved to {filepath}")


def load_checkpoint(filepath, model, optimizer=None):
    """
    加载模型检查点
    
    Args:
        filepath: 检查点文件路径
        model: PyTorch模型
        optimizer: 优化器（可选）
        
    Returns:
        tuple: (epoch, loss)
    """
    checkpoint = torch.load(filepath)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    if optimizer is not None:
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    epoch = checkpoint['epoch']
    loss = checkpoint['loss']
    
    print(f"Checkpoint loaded from {filepath}, epoch: {epoch}, loss: {loss}")
    return epoch, loss
